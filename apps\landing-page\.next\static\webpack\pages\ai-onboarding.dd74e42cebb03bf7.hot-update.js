"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ai-onboarding",{

/***/ "./src/pages/ai-onboarding.tsx":
/*!*************************************!*\
  !*** ./src/pages/ai-onboarding.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_seo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-seo */ \"../../node_modules/next-seo/lib/next-seo.module.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout/index.tsx\");\n/* harmony import */ var _components_ai_onboarding_RoleSelection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ai-onboarding/RoleSelection */ \"./src/components/ai-onboarding/RoleSelection.tsx\");\n/* harmony import */ var _components_ai_onboarding_DataCollectionForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ai-onboarding/DataCollectionForm */ \"./src/components/ai-onboarding/DataCollectionForm.tsx\");\n/* harmony import */ var _components_ai_onboarding_AIIntroduction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ai-onboarding/AIIntroduction */ \"./src/components/ai-onboarding/AIIntroduction.tsx\");\n/* harmony import */ var _components_ai_onboarding_ChatInterface__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ai-onboarding/ChatInterface */ \"./src/components/ai-onboarding/ChatInterface.tsx\");\n/* harmony import */ var _components_ai_onboarding_OnboardingProgress__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ai-onboarding/OnboardingProgress */ \"./src/components/ai-onboarding/OnboardingProgress.tsx\");\n/* harmony import */ var _components_ai_onboarding_CompletionCelebration__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ai-onboarding/CompletionCelebration */ \"./src/components/ai-onboarding/CompletionCelebration.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// AI Onboarding Components\n\n\n\n\n\n\nconst AIOnboardingPage = ()=>{\n    var _session_user, _session_user_name, _session_user1, _session_user_name1, _session_user2, _session_user3;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)([\n        \"common\",\n        \"ai-onboarding\"\n    ]);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const { role: urlRole } = router.query;\n    // State\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"role_selection\");\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [collectedData, setCollectedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [aiSession, setAiSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize role from URL or session with optimized redirect handling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Prevent flash content by handling redirects immediately\n        if (status === \"loading\") {\n            return; // Still loading, wait\n        }\n        if (status === \"unauthenticated\") {\n            // Immediate redirect for unauthenticated users\n            router.replace(\"/?auth=required\");\n            return;\n        }\n        if (!(session === null || session === void 0 ? void 0 : session.user)) {\n            // Immediate redirect if no user session\n            router.replace(\"/?auth=session-error\");\n            return;\n        }\n        // CRITICAL FIX: Handle completed onboarding users immediately to prevent flash\n        if (session.user.hasCompletedOnboarding) {\n            setIsRedirecting(true);\n            // Use window.location for immediate redirect without flash\n            const userRole = session.user.role;\n            switch(userRole){\n                case \"ADMIN\":\n                    window.location.replace(\"http://localhost:3001/dashboard\");\n                    break;\n                case \"EXPERT\":\n                    window.location.replace(\"http://localhost:3002/dashboard\");\n                    break;\n                case \"CLIENT\":\n                    window.location.replace(\"/?auth=success&role=client&onboarding=complete\");\n                    break;\n                default:\n                    window.location.replace(\"/?auth=success&onboarding=complete\");\n            }\n            return;\n        }\n        // CRITICAL FIX: Always start with role selection for users without completed onboarding\n        // This ensures the role selection page is always shown first\n        if (!session.user.hasCompletedOnboarding) {\n            // Check if user already has a role from previous session\n            if (session.user.role && (session.user.role === \"CLIENT\" || session.user.role === \"EXPERT\")) {\n                setSelectedRole(session.user.role);\n                setCurrentStep(\"data_collection\"); // Skip role selection if role already exists\n            } else if (urlRole && (urlRole === \"CLIENT\" || urlRole === \"EXPERT\")) {\n                setSelectedRole(urlRole);\n                setCurrentStep(\"data_collection\"); // Skip role selection if role in URL\n            } else {\n                // No role found, start with role selection\n                setCurrentStep(\"role_selection\");\n            }\n        }\n    }, [\n        status,\n        session,\n        urlRole,\n        router\n    ]);\n    /**\n   * Handle role selection\n   */ const handleRoleSelection = async (role)=>{\n        try {\n            setIsLoading(true);\n            setSelectedRole(role);\n            // CRITICAL FIX: Update the session with the selected role\n            // This ensures the role persists across page refreshes\n            await update({\n                role: role\n            });\n            console.log(\"✅ Role selected and session updated:\", role);\n            setCurrentStep(\"data_collection\");\n        } catch (error) {\n            console.error(\"❌ Failed to update session with role:\", error);\n            // Continue anyway - role will be saved during onboarding completion\n            setCurrentStep(\"data_collection\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Handle data collection completion\n   */ const handleDataCollectionComplete = (data)=>{\n        setCollectedData(data);\n        setCurrentStep(\"ai_introduction\");\n    };\n    /**\n   * Start AI conversation\n   */ const startAIConversation = async ()=>{\n        var _session_user;\n        if (!selectedRole || !(session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id) || !collectedData) return;\n        try {\n            var _collectedData_location;\n            setIsLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/ai/conversation/start\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userRole: selectedRole,\n                    language: \"ar\",\n                    sessionType: \"onboarding\",\n                    culturalContext: {\n                        location: ((_collectedData_location = collectedData.location) === null || _collectedData_location === void 0 ? void 0 : _collectedData_location.governorate) || \"سوريا\",\n                        dialect: \"general\"\n                    },\n                    userData: collectedData\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to start AI conversation\");\n            }\n            const data = await response.json();\n            if (data.success) {\n                setAiSession(data.data);\n                setCurrentStep(\"chat_conversation\");\n            } else {\n                throw new Error(data.message || \"Failed to start AI conversation\");\n            }\n        } catch (error) {\n            console.error(\"Failed to start AI conversation:\", error);\n            setError(error.message || \"حدث خطأ في بدء المحادثة الذكية\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Handle conversation completion\n   */ const handleConversationComplete = async (sessionData)=>{\n        try {\n            setIsLoading(true);\n            // Mark onboarding as completed in the database\n            const response = await fetch(\"/api/ai/onboarding/complete\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    sessionId: sessionData.id,\n                    extractedData: sessionData.extractedData\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to complete onboarding\");\n            }\n            setCurrentStep(\"completion\");\n            // Redirect to dashboard after celebration\n            setTimeout(()=>{\n                var _session_user;\n                const userRole = selectedRole || (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role);\n                switch(userRole){\n                    case \"ADMIN\":\n                        window.location.href = \"http://localhost:3001/dashboard\";\n                        break;\n                    case \"EXPERT\":\n                        window.location.href = \"http://localhost:3002/dashboard\";\n                        break;\n                    case \"CLIENT\":\n                        router.push(\"/?auth=success&role=client&onboarding=complete\");\n                        break;\n                    default:\n                        router.push(\"/?auth=success&onboarding=complete\");\n                }\n            }, 3000);\n        } catch (error) {\n            console.error(\"Failed to complete onboarding:\", error);\n            setError(error.message || \"حدث خطأ في إكمال التسجيل\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Handle logout\n   */ const handleLogout = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n            callbackUrl: \"/\"\n        });\n    };\n    // Enhanced loading state to prevent flash content\n    if (status === \"loading\" || (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.hasCompletedOnboarding) || isRedirecting) {\n        var _session_user4;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            hideHeaderFooter: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                style: {\n                    background: \"linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2d2d2d 50%, #1a1a1a 75%, #0a0a0a 100%)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-lg font-medium\",\n                            children: (session === null || session === void 0 ? void 0 : (_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : _session_user4.hasCompletedOnboarding) ? \"جاري التوجيه...\" : \"جاري التحميل...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md mx-auto p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-500 text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"حدث خطأ\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"w-full px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n                                    children: \"إعادة المحاولة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleLogout,\n                                    className: \"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"تسجيل الخروج\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_seo__WEBPACK_IMPORTED_MODULE_5__.NextSeo, {\n                title: \"التسجيل الذكي - فريلا سوريا\",\n                description: \"أكمل تسجيلك في منصة فريلا سوريا باستخدام المساعد الذكي\",\n                noindex: true,\n                nofollow: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                hideHeaderFooter: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen relative overflow-hidden\",\n                    style: {\n                        background: \"linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2d2d2d 50%, #1a1a1a 75%, #0a0a0a 100%)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-30\",\n                            style: {\n                                backgroundImage: \"\\n                radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),\\n                radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),\\n                radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.05) 0%, transparent 50%)\\n              \"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_OnboardingProgress__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            currentStep: currentStep,\n                            selectedRole: selectedRole,\n                            completionRate: (aiSession === null || aiSession === void 0 ? void 0 : aiSession.completionRate) || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-8 relative z-10\",\n                            children: [\n                                currentStep === \"role_selection\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_RoleSelection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    onRoleSelect: handleRoleSelection,\n                                    selectedRole: selectedRole,\n                                    isLoading: isLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"data_collection\" && selectedRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_DataCollectionForm__WEBPACK_IMPORTED_MODULE_8__.DataCollectionForm, {\n                                    onSubmit: handleDataCollectionComplete,\n                                    isLoading: isLoading,\n                                    selectedRole: selectedRole,\n                                    initialData: {\n                                        firstName: (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_name = _session_user1.name) === null || _session_user_name === void 0 ? void 0 : _session_user_name.split(\" \")[0]) || \"\",\n                                        lastName: (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : (_session_user_name1 = _session_user2.name) === null || _session_user_name1 === void 0 ? void 0 : _session_user_name1.split(\" \").slice(1).join(\" \")) || \"\",\n                                        email: (session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.email) || \"\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"ai_introduction\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_AIIntroduction__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    selectedRole: selectedRole,\n                                    onStartConversation: startAIConversation,\n                                    isLoading: isLoading,\n                                    onBack: ()=>{\n                                        // FIXED: Go back to role selection instead of data collection\n                                        // since data collection might not be properly initialized\n                                        setCurrentStep(\"role_selection\");\n                                        setSelectedRole(null);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"chat_conversation\" && aiSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_ChatInterface__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    session: aiSession,\n                                    onComplete: handleConversationComplete,\n                                    isLoading: isLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"completion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_CompletionCelebration__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    selectedRole: selectedRole,\n                                    extractedData: aiSession === null || aiSession === void 0 ? void 0 : aiSession.extractedData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AIOnboardingPage, \"V2QbimVrWxRjyo7j+pcOGm6F2Z0=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession\n    ];\n});\n_c = AIOnboardingPage;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AIOnboardingPage);\nvar _c;\n$RefreshReg$(_c, \"AIOnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/ai-onboarding.tsx\n"));

/***/ })

});