# 🧪 AUTHENTICATION FLOW TESTING GUIDE

## 🚀 **<PERSON><PERSON><PERSON><PERSON> START TESTING**

### **Step 1: Start the Landing Page Application**
```bash
cd apps/landing-page
npm run dev
```

### **Step 2: Clear Browser Data**
1. Open browser developer tools (F12)
2. Go to Application/Storage tab
3. Clear all cookies and local storage for `localhost:3004`
4. Close developer tools

### **Step 3: Test New User Flow**
1. Navigate to `http://localhost:3004`
2. Click "Sign In" button
3. Sign in with Google OAuth
4. **EXPECTED**: Redirect to `/ai-onboarding` with role selection page
5. **VERIFY**: Role selection page is stable (no flash/disappear)
6. Select a role (CLIENT or EXPERT)
7. **EXPECTED**: Proceed to data collection form
8. **VERIFY**: No redirect back to role selection

### **Step 4: Test Page Refresh**
1. After selecting role, refresh the page
2. **EXPECTED**: Should skip role selection and go to data collection
3. **VERIFY**: Role persists across page refresh

### **Step 5: Test Direct Navigation**
1. Navigate directly to `http://localhost:3004/ai-onboarding`
2. **EXPECTED**: Proper behavior based on user state
3. **VERIFY**: No infinite redirects or flash content

---

## 🔍 **DEBUGGING CHECKLIST**

### **Console Logs to Monitor:**
Open browser console and look for these logs:

✅ **JWT Token Creation:**
```
🔑 JWT callback triggered: { hasUser: true, tokenEmail: "<EMAIL>", ... }
✅ JWT token configured: { id: "...", role: undefined, hasCompletedOnboarding: false }
```

✅ **Session Configuration:**
```
📋 Session callback triggered: { tokenEmail: "<EMAIL>", ... }
✅ Session configured: { id: "...", role: undefined, hasCompletedOnboarding: false }
```

✅ **Role Selection:**
```
✅ Role selected and session updated: CLIENT
✅ Role updated in token: CLIENT
```

✅ **Redirect Logic:**
```
🔄 NextAuth redirect called with: { url: "...", baseUrl: "..." }
🤖 Redirecting ALL authenticated users to AI onboarding for proper flow handling
```

### **Session State Verification:**
In browser console, check session state:
```javascript
// Check current session
console.log('Current session:', await fetch('/api/auth/session').then(r => r.json()));
```

**Expected for new user:**
```json
{
  "user": {
    "id": "...",
    "email": "<EMAIL>",
    "role": undefined,
    "hasCompletedOnboarding": false
  }
}
```

**Expected after role selection:**
```json
{
  "user": {
    "id": "...",
    "email": "<EMAIL>",
    "role": "CLIENT",
    "hasCompletedOnboarding": false
  }
}
```

---

## ❌ **TROUBLESHOOTING COMMON ISSUES**

### **Issue 1: Role Selection Page Still Flashes**
**Symptoms**: Role selection appears briefly then disappears
**Solution**: Check console for errors, verify session state

### **Issue 2: Infinite Redirects**
**Symptoms**: Page keeps redirecting in a loop
**Solution**: Clear browser data and check NextAuth configuration

### **Issue 3: Role Not Persisting**
**Symptoms**: Role selection resets after page refresh
**Solution**: Verify session update is working, check JWT token

### **Issue 4: TypeScript Errors**
**Symptoms**: Build fails or type errors in console
**Solution**: Verify type definitions are updated correctly

---

## 📊 **SUCCESS CRITERIA**

### ✅ **New User Flow Success:**
- [ ] Role selection page displays immediately after sign-in
- [ ] Role selection page is stable (no flash/disappear)
- [ ] Role selection updates session correctly
- [ ] Flow proceeds to data collection after role selection
- [ ] No infinite redirects or errors

### ✅ **Session Persistence Success:**
- [ ] Selected role persists across page refreshes
- [ ] User doesn't see role selection again after selecting
- [ ] Direct navigation to `/ai-onboarding` works correctly

### ✅ **Returning User Success:**
- [ ] Users with completed onboarding redirect to dashboard
- [ ] No role selection page shown for completed users
- [ ] Proper dashboard redirect based on user role

---

## 🔧 **ADDITIONAL TESTING SCENARIOS**

### **Test Scenario A: Multiple Role Changes**
1. Select CLIENT role
2. Go back and select EXPERT role
3. Verify final role is EXPERT

### **Test Scenario B: Browser Back Button**
1. Complete role selection
2. Use browser back button
3. Verify proper behavior (should not go back to role selection)

### **Test Scenario C: Multiple Tabs**
1. Open multiple tabs with the application
2. Sign in on one tab
3. Verify behavior on other tabs

### **Test Scenario D: Sign Out and Sign In**
1. Complete partial onboarding
2. Sign out
3. Sign in again
4. Verify flow continues from where left off

---

## 📞 **REPORTING ISSUES**

If you encounter any issues during testing, please provide:

1. **Browser and version**
2. **Console error messages**
3. **Steps to reproduce**
4. **Expected vs actual behavior**
5. **Session state** (from `/api/auth/session`)
6. **Screenshots** if applicable

---

## 🎯 **EXPECTED FINAL RESULT**

After implementing these fixes, the authentication flow should be:

1. **Smooth and predictable** - No flash content or unexpected redirects
2. **Role-based** - Users must select roles before proceeding
3. **Persistent** - Roles and progress persist across page refreshes
4. **Consistent** - Same behavior regardless of how user reaches the page

The role selection page should be the **stable first step** for all new users, with no automatic bypassing or flash content issues.
