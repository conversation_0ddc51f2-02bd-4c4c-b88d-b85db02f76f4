import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession, signOut } from 'next-auth/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetServerSideProps } from 'next';
import { NextSeo } from 'next-seo';
import Layout from '@/components/Layout';

// AI Onboarding Components
import RoleSelection from '@/components/ai-onboarding/RoleSelection';
import { DataCollectionForm } from '@/components/ai-onboarding/DataCollectionForm';
import AIIntroduction from '@/components/ai-onboarding/AIIntroduction';
import ChatInterface from '@/components/ai-onboarding/ChatInterface';
import OnboardingProgress from '@/components/ai-onboarding/OnboardingProgress';
import CompletionCelebration from '@/components/ai-onboarding/CompletionCelebration';

// Types
interface AIOnboardingSession {
  id: string;
  currentStep: string;
  status: string;
  userRole: 'CLIENT' | 'EXPERT';
  messages: AIMessage[];
  extractedData: any;
  completionRate: number;
}

interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  confidence?: number;
  extractedData?: any;
}

type OnboardingStep = 'role_selection' | 'data_collection' | 'ai_introduction' | 'chat_conversation' | 'completion';

const AIOnboardingPage: React.FC = () => {
  const { t } = useTranslation(['common', 'ai-onboarding']);
  const router = useRouter();
  const { data: session, status, update } = useSession();
  const { role: urlRole } = router.query;

  // State
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('role_selection');
  const [selectedRole, setSelectedRole] = useState<'CLIENT' | 'EXPERT' | 'BUSINESS' | null>(null);
  const [collectedData, setCollectedData] = useState<any>(null);
  const [aiSession, setAiSession] = useState<AIOnboardingSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Initialize role from URL or session with optimized redirect handling
  useEffect(() => {
    // Prevent flash content by handling redirects immediately
    if (status === 'loading') {
      return; // Still loading, wait
    }

    if (status === 'unauthenticated') {
      // Immediate redirect for unauthenticated users
      router.replace('/?auth=required');
      return;
    }

    if (!session?.user) {
      // Immediate redirect if no user session
      router.replace('/?auth=session-error');
      return;
    }

    // CRITICAL FIX: Handle completed onboarding users immediately to prevent flash
    if (session.user.hasCompletedOnboarding) {
      setIsRedirecting(true);
      // Use window.location for immediate redirect without flash
      const userRole = session.user.role;
      switch (userRole) {
        case 'ADMIN':
          window.location.replace('http://localhost:3001/dashboard');
          break;
        case 'EXPERT':
          window.location.replace('http://localhost:3002/dashboard');
          break;
        case 'CLIENT':
          window.location.replace('/?auth=success&role=client&onboarding=complete');
          break;
        default:
          window.location.replace('/?auth=success&onboarding=complete');
      }
      return;
    }

    // CRITICAL FIX: Always start with role selection for users without completed onboarding
    // This ensures the role selection page is always shown first
    if (!session.user.hasCompletedOnboarding) {
      // Check if user already has a role from previous session
      if (session.user.role && (session.user.role === 'CLIENT' || session.user.role === 'EXPERT')) {
        setSelectedRole(session.user.role as 'CLIENT' | 'EXPERT');
        setCurrentStep('data_collection'); // Skip role selection if role already exists
      } else if (urlRole && (urlRole === 'CLIENT' || urlRole === 'EXPERT')) {
        setSelectedRole(urlRole as 'CLIENT' | 'EXPERT');
        setCurrentStep('data_collection'); // Skip role selection if role in URL
      } else {
        // No role found, start with role selection
        setCurrentStep('role_selection');
      }
    }
  }, [status, session, urlRole, router]);

  /**
   * Handle role selection
   */
  const handleRoleSelection = async (role: 'CLIENT' | 'EXPERT' | 'BUSINESS') => {
    try {
      setIsLoading(true);
      setSelectedRole(role);

      // CRITICAL FIX: Update the session with the selected role
      // This ensures the role persists across page refreshes
      await update({
        role: role,
      });

      console.log('✅ Role selected and session updated:', role);
      setCurrentStep('data_collection');
    } catch (error) {
      console.error('❌ Failed to update session with role:', error);
      // Continue anyway - role will be saved during onboarding completion
      setCurrentStep('data_collection');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle data collection completion
   */
  const handleDataCollectionComplete = (data: any) => {
    setCollectedData(data);
    setCurrentStep('ai_introduction');
  };

  /**
   * Start AI conversation
   */
  const startAIConversation = async () => {
    if (!selectedRole || !session?.user?.id || !collectedData) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/ai/conversation/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userRole: selectedRole,
          language: 'ar',
          sessionType: 'onboarding',
          culturalContext: {
            location: collectedData.location?.governorate || 'سوريا',
            dialect: 'general'
          },
          userData: collectedData
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start AI conversation');
      }

      const data = await response.json();

      if (data.success) {
        setAiSession(data.data);
        setCurrentStep('chat_conversation');
      } else {
        throw new Error(data.message || 'Failed to start AI conversation');
      }
    } catch (error: any) {
      console.error('Failed to start AI conversation:', error);
      setError(error.message || 'حدث خطأ في بدء المحادثة الذكية');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle conversation completion
   */
  const handleConversationComplete = async (sessionData: AIOnboardingSession) => {
    try {
      setIsLoading(true);

      // Mark onboarding as completed in the database
      const response = await fetch('/api/ai/onboarding/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: sessionData.id,
          extractedData: sessionData.extractedData,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to complete onboarding');
      }

      setCurrentStep('completion');
      
      // Redirect to dashboard after celebration
      setTimeout(() => {
        const userRole = selectedRole || session?.user?.role;
        switch (userRole) {
          case 'ADMIN':
            window.location.href = 'http://localhost:3001/dashboard';
            break;
          case 'EXPERT':
            window.location.href = 'http://localhost:3002/dashboard';
            break;
          case 'CLIENT':
            router.push('/?auth=success&role=client&onboarding=complete');
            break;
          default:
            router.push('/?auth=success&onboarding=complete');
        }
      }, 3000);

    } catch (error: any) {
      console.error('Failed to complete onboarding:', error);
      setError(error.message || 'حدث خطأ في إكمال التسجيل');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle logout
   */
  const handleLogout = () => {
    signOut({ callbackUrl: '/' });
  };

  // Enhanced loading state to prevent flash content
  if (status === 'loading' || (session?.user?.hasCompletedOnboarding) || isRedirecting) {
    return (
      <Layout hideHeaderFooter={true}>
        <div
          className="min-h-screen flex items-center justify-center"
          style={{
            background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2d2d2d 50%, #1a1a1a 75%, #0a0a0a 100%)',
          }}
        >
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
            <p className="text-gray-300 text-lg font-medium">
              {session?.user?.hasCompletedOnboarding ? 'جاري التوجيه...' : 'جاري التحميل...'}
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  // Error state
  if (error) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              حدث خطأ
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>
            <div className="space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                إعادة المحاولة
              </button>
              <button
                type="button"
                onClick={handleLogout}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <NextSeo
        title="التسجيل الذكي - فريلا سوريا"
        description="أكمل تسجيلك في منصة فريلا سوريا باستخدام المساعد الذكي"
        noindex={true}
        nofollow={true}
      />

      <Layout hideHeaderFooter={true}>
        <div
          className="min-h-screen relative overflow-hidden"
          style={{
            background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2d2d2d 50%, #1a1a1a 75%, #0a0a0a 100%)',
          }}
        >
          {/* Background Pattern */}
          <div
            className="absolute inset-0 opacity-30"
            style={{
              backgroundImage: `
                radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.05) 0%, transparent 50%)
              `,
            }}
          />

          {/* Progress Indicator */}
          <OnboardingProgress
            currentStep={currentStep}
            selectedRole={selectedRole}
            completionRate={aiSession?.completionRate || 0}
          />

          {/* Main Content */}
          <div className="container mx-auto px-4 py-8 relative z-10">
            {currentStep === 'role_selection' && (
              <RoleSelection
                onRoleSelect={handleRoleSelection}
                selectedRole={selectedRole as 'CLIENT' | 'EXPERT' | null}
                isLoading={isLoading}
              />
            )}

            {currentStep === 'data_collection' && selectedRole && (
              <DataCollectionForm
                onSubmit={handleDataCollectionComplete}
                isLoading={isLoading}
                selectedRole={selectedRole}
                initialData={{
                  firstName: session?.user?.name?.split(' ')[0] || '',
                  lastName: session?.user?.name?.split(' ').slice(1).join(' ') || '',
                  email: session?.user?.email || '',
                }}
              />
            )}

            {currentStep === 'ai_introduction' && (
              <AIIntroduction
                selectedRole={selectedRole as 'CLIENT' | 'EXPERT'}
                onStartConversation={startAIConversation}
                isLoading={isLoading}
                onBack={() => {
                  // FIXED: Go back to role selection instead of data collection
                  // since data collection might not be properly initialized
                  setCurrentStep('role_selection');
                  setSelectedRole(null);
                }}
              />
            )}

            {currentStep === 'chat_conversation' && aiSession && (
              <ChatInterface
                session={aiSession}
                onComplete={handleConversationComplete}
                isLoading={isLoading}
              />
            )}

            {currentStep === 'completion' && (
              <CompletionCelebration
                selectedRole={selectedRole as 'CLIENT' | 'EXPERT'}
                extractedData={aiSession?.extractedData}
              />
            )}
          </div>
        </div>
      </Layout>
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ar', ['common', 'landing', 'auth', 'ai-onboarding'])),
    },
  };
};

export default AIOnboardingPage;
