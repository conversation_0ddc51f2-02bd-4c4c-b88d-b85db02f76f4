"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/ai-onboarding",{

/***/ "./src/pages/ai-onboarding.tsx":
/*!*************************************!*\
  !*** ./src/pages/ai-onboarding.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-i18next */ \"../../node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_seo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-seo */ \"../../node_modules/next-seo/lib/next-seo.module.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout/index.tsx\");\n/* harmony import */ var _components_ai_onboarding_RoleSelection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ai-onboarding/RoleSelection */ \"./src/components/ai-onboarding/RoleSelection.tsx\");\n/* harmony import */ var _components_ai_onboarding_DataCollectionForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ai-onboarding/DataCollectionForm */ \"./src/components/ai-onboarding/DataCollectionForm.tsx\");\n/* harmony import */ var _components_ai_onboarding_AIIntroduction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ai-onboarding/AIIntroduction */ \"./src/components/ai-onboarding/AIIntroduction.tsx\");\n/* harmony import */ var _components_ai_onboarding_ChatInterface__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ai-onboarding/ChatInterface */ \"./src/components/ai-onboarding/ChatInterface.tsx\");\n/* harmony import */ var _components_ai_onboarding_OnboardingProgress__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ai-onboarding/OnboardingProgress */ \"./src/components/ai-onboarding/OnboardingProgress.tsx\");\n/* harmony import */ var _components_ai_onboarding_CompletionCelebration__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ai-onboarding/CompletionCelebration */ \"./src/components/ai-onboarding/CompletionCelebration.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// AI Onboarding Components\n\n\n\n\n\n\nconst AIOnboardingPage = ()=>{\n    var _session_user, _session_user_name, _session_user1, _session_user_name1, _session_user2, _session_user3;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)([\n        \"common\",\n        \"ai-onboarding\"\n    ]);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const { role: urlRole } = router.query;\n    // State\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"role_selection\");\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [collectedData, setCollectedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [aiSession, setAiSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize role from URL or session with optimized redirect handling\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Prevent flash content by handling redirects immediately\n        if (status === \"loading\") {\n            return; // Still loading, wait\n        }\n        if (status === \"unauthenticated\") {\n            // Immediate redirect for unauthenticated users\n            router.replace(\"/?auth=required\");\n            return;\n        }\n        if (!(session === null || session === void 0 ? void 0 : session.user)) {\n            // Immediate redirect if no user session\n            router.replace(\"/?auth=session-error\");\n            return;\n        }\n        // CRITICAL FIX: Handle completed onboarding users immediately to prevent flash\n        if (session.user.hasCompletedOnboarding) {\n            setIsRedirecting(true);\n            // Use window.location for immediate redirect without flash\n            const userRole = session.user.role;\n            switch(userRole){\n                case \"ADMIN\":\n                    window.location.replace(\"http://localhost:3001/dashboard\");\n                    break;\n                case \"EXPERT\":\n                    window.location.replace(\"http://localhost:3002/dashboard\");\n                    break;\n                case \"CLIENT\":\n                    window.location.replace(\"/?auth=success&role=client&onboarding=complete\");\n                    break;\n                default:\n                    window.location.replace(\"/?auth=success&onboarding=complete\");\n            }\n            return;\n        }\n        // CRITICAL FIX: Always start with role selection for users without completed onboarding\n        // This ensures the role selection page is always shown first\n        if (!session.user.hasCompletedOnboarding) {\n            // Check if user already has a role from previous session\n            if (session.user.role && (session.user.role === \"CLIENT\" || session.user.role === \"EXPERT\")) {\n                setSelectedRole(session.user.role);\n                setCurrentStep(\"data_collection\"); // Skip role selection if role already exists\n            } else if (urlRole && (urlRole === \"CLIENT\" || urlRole === \"EXPERT\")) {\n                setSelectedRole(urlRole);\n                setCurrentStep(\"data_collection\"); // Skip role selection if role in URL\n            } else {\n                // No role found, start with role selection\n                setCurrentStep(\"role_selection\");\n            }\n        }\n    }, [\n        status,\n        session,\n        urlRole,\n        router\n    ]);\n    /**\n   * Handle role selection\n   */ const handleRoleSelection = (role)=>{\n        setSelectedRole(role);\n        setCurrentStep(\"data_collection\");\n    };\n    /**\n   * Handle data collection completion\n   */ const handleDataCollectionComplete = (data)=>{\n        setCollectedData(data);\n        setCurrentStep(\"ai_introduction\");\n    };\n    /**\n   * Start AI conversation\n   */ const startAIConversation = async ()=>{\n        var _session_user;\n        if (!selectedRole || !(session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id) || !collectedData) return;\n        try {\n            var _collectedData_location;\n            setIsLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/ai/conversation/start\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userRole: selectedRole,\n                    language: \"ar\",\n                    sessionType: \"onboarding\",\n                    culturalContext: {\n                        location: ((_collectedData_location = collectedData.location) === null || _collectedData_location === void 0 ? void 0 : _collectedData_location.governorate) || \"سوريا\",\n                        dialect: \"general\"\n                    },\n                    userData: collectedData\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to start AI conversation\");\n            }\n            const data = await response.json();\n            if (data.success) {\n                setAiSession(data.data);\n                setCurrentStep(\"chat_conversation\");\n            } else {\n                throw new Error(data.message || \"Failed to start AI conversation\");\n            }\n        } catch (error) {\n            console.error(\"Failed to start AI conversation:\", error);\n            setError(error.message || \"حدث خطأ في بدء المحادثة الذكية\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Handle conversation completion\n   */ const handleConversationComplete = async (sessionData)=>{\n        try {\n            setIsLoading(true);\n            // Mark onboarding as completed in the database\n            const response = await fetch(\"/api/ai/onboarding/complete\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    sessionId: sessionData.id,\n                    extractedData: sessionData.extractedData\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to complete onboarding\");\n            }\n            setCurrentStep(\"completion\");\n            // Redirect to dashboard after celebration\n            setTimeout(()=>{\n                var _session_user;\n                const userRole = selectedRole || (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role);\n                switch(userRole){\n                    case \"ADMIN\":\n                        window.location.href = \"http://localhost:3001/dashboard\";\n                        break;\n                    case \"EXPERT\":\n                        window.location.href = \"http://localhost:3002/dashboard\";\n                        break;\n                    case \"CLIENT\":\n                        router.push(\"/?auth=success&role=client&onboarding=complete\");\n                        break;\n                    default:\n                        router.push(\"/?auth=success&onboarding=complete\");\n                }\n            }, 3000);\n        } catch (error) {\n            console.error(\"Failed to complete onboarding:\", error);\n            setError(error.message || \"حدث خطأ في إكمال التسجيل\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\n   * Handle logout\n   */ const handleLogout = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n            callbackUrl: \"/\"\n        });\n    };\n    // Enhanced loading state to prevent flash content\n    if (status === \"loading\" || (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.hasCompletedOnboarding) || isRedirecting) {\n        var _session_user4;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            hideHeaderFooter: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                style: {\n                    background: \"linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2d2d2d 50%, #1a1a1a 75%, #0a0a0a 100%)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-lg font-medium\",\n                            children: (session === null || session === void 0 ? void 0 : (_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : _session_user4.hasCompletedOnboarding) ? \"جاري التوجيه...\" : \"جاري التحميل...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md mx-auto p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-500 text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                            children: \"حدث خطأ\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"w-full px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n                                    children: \"إعادة المحاولة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleLogout,\n                                    className: \"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"تسجيل الخروج\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_seo__WEBPACK_IMPORTED_MODULE_5__.NextSeo, {\n                title: \"التسجيل الذكي - فريلا سوريا\",\n                description: \"أكمل تسجيلك في منصة فريلا سوريا باستخدام المساعد الذكي\",\n                noindex: true,\n                nofollow: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                hideHeaderFooter: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen relative overflow-hidden\",\n                    style: {\n                        background: \"linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2d2d2d 50%, #1a1a1a 75%, #0a0a0a 100%)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-30\",\n                            style: {\n                                backgroundImage: \"\\n                radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),\\n                radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),\\n                radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.05) 0%, transparent 50%)\\n              \"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_OnboardingProgress__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            currentStep: currentStep,\n                            selectedRole: selectedRole,\n                            completionRate: (aiSession === null || aiSession === void 0 ? void 0 : aiSession.completionRate) || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-8 relative z-10\",\n                            children: [\n                                currentStep === \"role_selection\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_RoleSelection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    onRoleSelect: handleRoleSelection,\n                                    selectedRole: selectedRole,\n                                    isLoading: isLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"data_collection\" && selectedRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_DataCollectionForm__WEBPACK_IMPORTED_MODULE_8__.DataCollectionForm, {\n                                    onSubmit: handleDataCollectionComplete,\n                                    isLoading: isLoading,\n                                    selectedRole: selectedRole,\n                                    initialData: {\n                                        firstName: (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_name = _session_user1.name) === null || _session_user_name === void 0 ? void 0 : _session_user_name.split(\" \")[0]) || \"\",\n                                        lastName: (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : (_session_user_name1 = _session_user2.name) === null || _session_user_name1 === void 0 ? void 0 : _session_user_name1.split(\" \").slice(1).join(\" \")) || \"\",\n                                        email: (session === null || session === void 0 ? void 0 : (_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : _session_user3.email) || \"\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"ai_introduction\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_AIIntroduction__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    selectedRole: selectedRole,\n                                    onStartConversation: startAIConversation,\n                                    isLoading: isLoading,\n                                    onBack: ()=>{\n                                        // FIXED: Go back to role selection instead of data collection\n                                        // since data collection might not be properly initialized\n                                        setCurrentStep(\"role_selection\");\n                                        setSelectedRole(null);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"chat_conversation\" && aiSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_ChatInterface__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    session: aiSession,\n                                    onComplete: handleConversationComplete,\n                                    isLoading: isLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentStep === \"completion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ai_onboarding_CompletionCelebration__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    selectedRole: selectedRole,\n                                    extractedData: aiSession === null || aiSession === void 0 ? void 0 : aiSession.extractedData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Freela\\\\apps\\\\landing-page\\\\src\\\\pages\\\\ai-onboarding.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AIOnboardingPage, \"V2QbimVrWxRjyo7j+pcOGm6F2Z0=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession\n    ];\n});\n_c = AIOnboardingPage;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AIOnboardingPage);\nvar _c;\n$RefreshReg$(_c, \"AIOnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/ai-onboarding.tsx\n"));

/***/ })

});