import { DefaultSession, DefaultUser } from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      role?: string; // CRITICAL FIX: Make role optional for new users
      status: string;
      language: string;
      firstName: string;
      lastName: string;
      avatar?: string;
      hasCompletedOnboarding: boolean;
    } & DefaultSession['user'];
  }

  interface User extends DefaultUser {
    role?: string;
    status?: string;
    language?: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    hasCompletedOnboarding?: boolean;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role?: string; // CRITICAL FIX: Make role optional for new users
    status: string;
    language: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    hasCompletedOnboarding: boolean;
  }
}
