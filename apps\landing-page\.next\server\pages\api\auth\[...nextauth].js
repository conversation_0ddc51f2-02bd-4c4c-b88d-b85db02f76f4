"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].ts */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// import { PrismaAdapter } from '@next-auth/prisma-adapter';\n// import { prisma } from '@freela/database';\n// import { Prisma } from '@prisma/client';\nconst authOptions = {\n    // adapter: PrismaAdapter(prisma), // Temporarily disabled for testing\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                console.log(\"\\uD83D\\uDD10 SignIn callback triggered:\", {\n                    user: user.email,\n                    provider: account?.provider,\n                    profile: profile?.name\n                });\n                if (account?.provider === \"google\") {\n                    console.log(\"✅ Google OAuth sign-in successful for:\", user.email);\n                    // For now, we'll skip database operations and just allow sign-in\n                    // TODO: Re-enable database operations once connection is fixed\n                    return true;\n                }\n                return true;\n            } catch (error) {\n                console.error(\"❌ Error during sign in:\", error);\n                return false;\n            }\n        },\n        async jwt ({ token, user, trigger, session }) {\n            console.log(\"\\uD83D\\uDD11 JWT callback triggered:\", {\n                hasUser: !!user,\n                tokenEmail: token.email,\n                userEmail: user?.email,\n                trigger\n            });\n            if (user) {\n                console.log(\"\\uD83D\\uDC64 Setting up JWT token for new user:\", user.email);\n                // For testing, we'll set default values without database lookup\n                token.id = user.id || user.email || \"temp-id\"; // Use email as fallback ID\n                token.role = undefined; // CRITICAL FIX: No default role - user must select during onboarding\n                token.status = \"ACTIVE\";\n                token.language = \"ar\";\n                token.firstName = user.name?.split(\" \")[0] || \"User\";\n                token.lastName = user.name?.split(\" \").slice(1).join(\" \") || \"\";\n                token.avatar = user.image || null;\n                token.hasCompletedOnboarding = false; // Always false for new users\n                console.log(\"✅ JWT token configured:\", {\n                    id: token.id,\n                    role: token.role,\n                    hasCompletedOnboarding: token.hasCompletedOnboarding\n                });\n            }\n            // Handle onboarding completion updates\n            if (trigger === \"update\" && session) {\n                console.log(\"\\uD83D\\uDD04 JWT token update triggered with data:\", session);\n                // Update role if provided\n                if (session.role) {\n                    token.role = session.role;\n                    console.log(\"✅ Role updated in token:\", session.role);\n                }\n                // Update onboarding completion status if provided\n                if (session.hasCompletedOnboarding !== undefined) {\n                    token.hasCompletedOnboarding = session.hasCompletedOnboarding;\n                    console.log(\"✅ Onboarding status updated in token:\", session.hasCompletedOnboarding);\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"\\uD83D\\uDCCB Session callback triggered:\", {\n                tokenEmail: token.email,\n                sessionEmail: session.user?.email,\n                hasCompletedOnboarding: token.hasCompletedOnboarding\n            });\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.language = token.language;\n                session.user.firstName = token.firstName;\n                session.user.lastName = token.lastName;\n                session.user.avatar = token.avatar;\n                session.user.hasCompletedOnboarding = token.hasCompletedOnboarding;\n                console.log(\"✅ Session configured:\", {\n                    id: session.user.id,\n                    role: session.user.role,\n                    hasCompletedOnboarding: session.user.hasCompletedOnboarding\n                });\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Handle role-based redirects after successful authentication\n            try {\n                console.log(\"\\uD83D\\uDD04 NextAuth redirect called with:\", {\n                    url,\n                    baseUrl\n                });\n                // Check if this is a post-authentication callback\n                if (url.includes(\"/api/auth/callback\") || url === baseUrl || url === `${baseUrl}/`) {\n                    console.log(\"\\uD83D\\uDD04 Post-authentication callback detected\");\n                    // CRITICAL FIX: Always redirect to AI onboarding first for proper flow\n                    // The AI onboarding page will handle the logic for completed users\n                    console.log(\"\\uD83E\\uDD16 Redirecting ALL authenticated users to AI onboarding for proper flow handling\");\n                    return `${baseUrl}/ai-onboarding`;\n                }\n                // Handle relative URLs\n                if (url.startsWith(\"/\")) {\n                    const fullUrl = `${baseUrl}${url}`;\n                    console.log(\"\\uD83D\\uDD04 Converting relative URL to absolute:\", fullUrl);\n                    return fullUrl;\n                }\n                // Handle same-origin URLs\n                if (url.startsWith(baseUrl)) {\n                    console.log(\"\\uD83D\\uDD04 Same-origin URL redirect:\", url);\n                    return url;\n                }\n                // Default fallback - redirect to landing page for safety\n                console.log(\"\\uD83C\\uDFE0 Fallback redirect to landing page\");\n                return `${baseUrl}/?auth=success`;\n            } catch (error) {\n                console.error(\"❌ Redirect error:\", error);\n                return `${baseUrl}/?auth=error`;\n            }\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    events: {\n        async signIn ({ user, account }) {\n            console.log(`🎉 User ${user.email} signed in with ${account?.provider}`);\n        // TODO: Re-enable database operations once connection is fixed\n        },\n        async signOut ({ token }) {\n            console.log(`👋 User ${token?.email} signed out`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvcGFnZXMvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBc0Q7QUFDRTtBQUN4RCw2REFBNkQ7QUFDN0QsNkNBQTZDO0FBQzdDLDJDQUEyQztBQUVwQyxNQUFNRSxjQUErQjtJQUMxQyxzRUFBc0U7SUFDdEVDLFdBQVc7UUFDVEYsaUVBQWNBLENBQUM7WUFDYkcsVUFBVUMsUUFBUUMsR0FBRyxDQUFDQyxnQkFBZ0IsSUFBSTtZQUMxQ0MsY0FBY0gsUUFBUUMsR0FBRyxDQUFDRyxvQkFBb0IsSUFBSTtZQUNsREMsZUFBZTtnQkFDYkMsUUFBUTtvQkFDTkMsUUFBUTtvQkFDUkMsYUFBYTtvQkFDYkMsZUFBZTtnQkFDakI7WUFDRjtRQUNGO0tBQ0Q7SUFDREMsT0FBTztRQUNMQyxRQUFRO1FBQ1JDLE9BQU87SUFDVDtJQUNBQyxXQUFXO1FBQ1QsTUFBTUYsUUFBTyxFQUFFRyxJQUFJLEVBQUVDLE9BQU8sRUFBRUMsT0FBTyxFQUFFO1lBQ3JDLElBQUk7Z0JBQ0ZDLFFBQVFDLEdBQUcsQ0FBQywyQ0FBaUM7b0JBQzNDSixNQUFNQSxLQUFLSyxLQUFLO29CQUNoQkMsVUFBVUwsU0FBU0s7b0JBQ25CSixTQUFTQSxTQUFTSztnQkFDcEI7Z0JBRUEsSUFBSU4sU0FBU0ssYUFBYSxVQUFVO29CQUNsQ0gsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ0osS0FBS0ssS0FBSztvQkFDaEUsaUVBQWlFO29CQUNqRSwrREFBK0Q7b0JBQy9ELE9BQU87Z0JBQ1Q7Z0JBQ0EsT0FBTztZQUNULEVBQUUsT0FBT1AsT0FBTztnQkFDZEssUUFBUUwsS0FBSyxDQUFDLDJCQUEyQkE7Z0JBQ3pDLE9BQU87WUFDVDtRQUNGO1FBQ0EsTUFBTVUsS0FBSSxFQUFFQyxLQUFLLEVBQUVULElBQUksRUFBRVUsT0FBTyxFQUFFQyxPQUFPLEVBQUU7WUFDekNSLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOEI7Z0JBQ3hDUSxTQUFTLENBQUMsQ0FBQ1o7Z0JBQ1hhLFlBQVlKLE1BQU1KLEtBQUs7Z0JBQ3ZCUyxXQUFXZCxNQUFNSztnQkFDakJLO1lBQ0Y7WUFFQSxJQUFJVixNQUFNO2dCQUNSRyxRQUFRQyxHQUFHLENBQUMsbURBQXlDSixLQUFLSyxLQUFLO2dCQUMvRCxnRUFBZ0U7Z0JBQ2hFSSxNQUFNTSxFQUFFLEdBQUdmLEtBQUtlLEVBQUUsSUFBSWYsS0FBS0ssS0FBSyxJQUFJLFdBQVcsMkJBQTJCO2dCQUMxRUksTUFBTU8sSUFBSSxHQUFHQyxXQUFXLHFFQUFxRTtnQkFDN0ZSLE1BQU1TLE1BQU0sR0FBRztnQkFDZlQsTUFBTVUsUUFBUSxHQUFHO2dCQUNqQlYsTUFBTVcsU0FBUyxHQUFHcEIsS0FBS08sSUFBSSxFQUFFYyxNQUFNLElBQUksQ0FBQyxFQUFFLElBQUk7Z0JBQzlDWixNQUFNYSxRQUFRLEdBQUd0QixLQUFLTyxJQUFJLEVBQUVjLE1BQU0sS0FBS0UsTUFBTSxHQUFHQyxLQUFLLFFBQVE7Z0JBQzdEZixNQUFNZ0IsTUFBTSxHQUFHekIsS0FBSzBCLEtBQUssSUFBSTtnQkFDN0JqQixNQUFNa0Isc0JBQXNCLEdBQUcsT0FBTyw2QkFBNkI7Z0JBRW5FeEIsUUFBUUMsR0FBRyxDQUFDLDJCQUEyQjtvQkFDckNXLElBQUlOLE1BQU1NLEVBQUU7b0JBQ1pDLE1BQU1QLE1BQU1PLElBQUk7b0JBQ2hCVyx3QkFBd0JsQixNQUFNa0Isc0JBQXNCO2dCQUN0RDtZQUNGO1lBRUEsdUNBQXVDO1lBQ3ZDLElBQUlqQixZQUFZLFlBQVlDLFNBQVM7Z0JBQ25DUixRQUFRQyxHQUFHLENBQUMsc0RBQTRDTztnQkFFeEQsMEJBQTBCO2dCQUMxQixJQUFJQSxRQUFRSyxJQUFJLEVBQUU7b0JBQ2hCUCxNQUFNTyxJQUFJLEdBQUdMLFFBQVFLLElBQUk7b0JBQ3pCYixRQUFRQyxHQUFHLENBQUMsNEJBQTRCTyxRQUFRSyxJQUFJO2dCQUN0RDtnQkFFQSxrREFBa0Q7Z0JBQ2xELElBQUlMLFFBQVFnQixzQkFBc0IsS0FBS1YsV0FBVztvQkFDaERSLE1BQU1rQixzQkFBc0IsR0FBR2hCLFFBQVFnQixzQkFBc0I7b0JBQzdEeEIsUUFBUUMsR0FBRyxDQUFDLHlDQUF5Q08sUUFBUWdCLHNCQUFzQjtnQkFDckY7WUFDRjtZQUVBLE9BQU9sQjtRQUNUO1FBQ0EsTUFBTUUsU0FBUSxFQUFFQSxPQUFPLEVBQUVGLEtBQUssRUFBRTtZQUM5Qk4sUUFBUUMsR0FBRyxDQUFDLDRDQUFrQztnQkFDNUNTLFlBQVlKLE1BQU1KLEtBQUs7Z0JBQ3ZCdUIsY0FBY2pCLFFBQVFYLElBQUksRUFBRUs7Z0JBQzVCc0Isd0JBQXdCbEIsTUFBTWtCLHNCQUFzQjtZQUN0RDtZQUVBLElBQUlsQixPQUFPO2dCQUNURSxRQUFRWCxJQUFJLENBQUNlLEVBQUUsR0FBR04sTUFBTU0sRUFBRTtnQkFDMUJKLFFBQVFYLElBQUksQ0FBQ2dCLElBQUksR0FBR1AsTUFBTU8sSUFBSTtnQkFDOUJMLFFBQVFYLElBQUksQ0FBQ2tCLE1BQU0sR0FBR1QsTUFBTVMsTUFBTTtnQkFDbENQLFFBQVFYLElBQUksQ0FBQ21CLFFBQVEsR0FBR1YsTUFBTVUsUUFBUTtnQkFDdENSLFFBQVFYLElBQUksQ0FBQ29CLFNBQVMsR0FBR1gsTUFBTVcsU0FBUztnQkFDeENULFFBQVFYLElBQUksQ0FBQ3NCLFFBQVEsR0FBR2IsTUFBTWEsUUFBUTtnQkFDdENYLFFBQVFYLElBQUksQ0FBQ3lCLE1BQU0sR0FBR2hCLE1BQU1nQixNQUFNO2dCQUNsQ2QsUUFBUVgsSUFBSSxDQUFDMkIsc0JBQXNCLEdBQUdsQixNQUFNa0Isc0JBQXNCO2dCQUVsRXhCLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUI7b0JBQ25DVyxJQUFJSixRQUFRWCxJQUFJLENBQUNlLEVBQUU7b0JBQ25CQyxNQUFNTCxRQUFRWCxJQUFJLENBQUNnQixJQUFJO29CQUN2Qlcsd0JBQXdCaEIsUUFBUVgsSUFBSSxDQUFDMkIsc0JBQXNCO2dCQUM3RDtZQUNGO1lBQ0EsT0FBT2hCO1FBQ1Q7UUFDQSxNQUFNa0IsVUFBUyxFQUFFQyxHQUFHLEVBQUVDLE9BQU8sRUFBRTtZQUM3Qiw4REFBOEQ7WUFDOUQsSUFBSTtnQkFDRjVCLFFBQVFDLEdBQUcsQ0FBQywrQ0FBcUM7b0JBQUUwQjtvQkFBS0M7Z0JBQVE7Z0JBRWhFLGtEQUFrRDtnQkFDbEQsSUFBSUQsSUFBSUUsUUFBUSxDQUFDLHlCQUF5QkYsUUFBUUMsV0FBV0QsUUFBUSxDQUFDLEVBQUVDLFFBQVEsQ0FBQyxDQUFDLEVBQUU7b0JBQ2xGNUIsUUFBUUMsR0FBRyxDQUFDO29CQUVaLHVFQUF1RTtvQkFDdkUsbUVBQW1FO29CQUNuRUQsUUFBUUMsR0FBRyxDQUFDO29CQUNaLE9BQU8sQ0FBQyxFQUFFMkIsUUFBUSxjQUFjLENBQUM7Z0JBQ25DO2dCQUVBLHVCQUF1QjtnQkFDdkIsSUFBSUQsSUFBSUcsVUFBVSxDQUFDLE1BQU07b0JBQ3ZCLE1BQU1DLFVBQVUsQ0FBQyxFQUFFSCxRQUFRLEVBQUVELElBQUksQ0FBQztvQkFDbEMzQixRQUFRQyxHQUFHLENBQUMscURBQTJDOEI7b0JBQ3ZELE9BQU9BO2dCQUNUO2dCQUVBLDBCQUEwQjtnQkFDMUIsSUFBSUosSUFBSUcsVUFBVSxDQUFDRixVQUFVO29CQUMzQjVCLFFBQVFDLEdBQUcsQ0FBQywwQ0FBZ0MwQjtvQkFDNUMsT0FBT0E7Z0JBQ1Q7Z0JBRUEseURBQXlEO2dCQUN6RDNCLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixPQUFPLENBQUMsRUFBRTJCLFFBQVEsY0FBYyxDQUFDO1lBRW5DLEVBQUUsT0FBT2pDLE9BQU87Z0JBQ2RLLFFBQVFMLEtBQUssQ0FBQyxxQkFBcUJBO2dCQUNuQyxPQUFPLENBQUMsRUFBRWlDLFFBQVEsWUFBWSxDQUFDO1lBQ2pDO1FBQ0Y7SUFDRjtJQUNBcEIsU0FBUztRQUNQd0IsVUFBVTtRQUNWQyxRQUFRLEtBQUssS0FBSyxLQUFLO0lBQ3pCO0lBQ0E1QixLQUFLO1FBQ0g0QixRQUFRLEtBQUssS0FBSyxLQUFLO0lBQ3pCO0lBQ0FDLFFBQVE7UUFDTixNQUFNeEMsUUFBTyxFQUFFRyxJQUFJLEVBQUVDLE9BQU8sRUFBRTtZQUM1QkUsUUFBUUMsR0FBRyxDQUFDLENBQUMsUUFBUSxFQUFFSixLQUFLSyxLQUFLLENBQUMsZ0JBQWdCLEVBQUVKLFNBQVNLLFNBQVMsQ0FBQztRQUN2RSwrREFBK0Q7UUFDakU7UUFDQSxNQUFNZ0MsU0FBUSxFQUFFN0IsS0FBSyxFQUFFO1lBQ3JCTixRQUFRQyxHQUFHLENBQUMsQ0FBQyxRQUFRLEVBQUVLLE9BQU9KLE1BQU0sV0FBVyxDQUFDO1FBQ2xEO0lBQ0Y7SUFDQWtDLE9BQU9yRCxrQkFBeUI7QUFDbEMsRUFBRTtBQUVGLGlFQUFlTCxnREFBUUEsQ0FBQ0UsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BmcmVlbGEvbGFuZGluZy1wYWdlLy4vc3JjL3BhZ2VzL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0udHM/NTBhMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTmV4dEF1dGgsIHsgTmV4dEF1dGhPcHRpb25zIH0gZnJvbSAnbmV4dC1hdXRoJztcclxuaW1wb3J0IEdvb2dsZVByb3ZpZGVyIGZyb20gJ25leHQtYXV0aC9wcm92aWRlcnMvZ29vZ2xlJztcclxuLy8gaW1wb3J0IHsgUHJpc21hQWRhcHRlciB9IGZyb20gJ0BuZXh0LWF1dGgvcHJpc21hLWFkYXB0ZXInO1xyXG4vLyBpbXBvcnQgeyBwcmlzbWEgfSBmcm9tICdAZnJlZWxhL2RhdGFiYXNlJztcclxuLy8gaW1wb3J0IHsgUHJpc21hIH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xyXG5cclxuZXhwb3J0IGNvbnN0IGF1dGhPcHRpb25zOiBOZXh0QXV0aE9wdGlvbnMgPSB7XHJcbiAgLy8gYWRhcHRlcjogUHJpc21hQWRhcHRlcihwcmlzbWEpLCAvLyBUZW1wb3JhcmlseSBkaXNhYmxlZCBmb3IgdGVzdGluZ1xyXG4gIHByb3ZpZGVyczogW1xyXG4gICAgR29vZ2xlUHJvdmlkZXIoe1xyXG4gICAgICBjbGllbnRJZDogcHJvY2Vzcy5lbnYuR09PR0xFX0NMSUVOVF9JRCB8fCAnJyxcclxuICAgICAgY2xpZW50U2VjcmV0OiBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX1NFQ1JFVCB8fCAnJyxcclxuICAgICAgYXV0aG9yaXphdGlvbjoge1xyXG4gICAgICAgIHBhcmFtczoge1xyXG4gICAgICAgICAgcHJvbXB0OiBcImNvbnNlbnRcIixcclxuICAgICAgICAgIGFjY2Vzc190eXBlOiBcIm9mZmxpbmVcIixcclxuICAgICAgICAgIHJlc3BvbnNlX3R5cGU6IFwiY29kZVwiXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9KSxcclxuICBdLFxyXG4gIHBhZ2VzOiB7XHJcbiAgICBzaWduSW46ICcvJyxcclxuICAgIGVycm9yOiAnL2F1dGgvZXJyb3InLFxyXG4gIH0sXHJcbiAgY2FsbGJhY2tzOiB7XHJcbiAgICBhc3luYyBzaWduSW4oeyB1c2VyLCBhY2NvdW50LCBwcm9maWxlIH0pIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UkCBTaWduSW4gY2FsbGJhY2sgdHJpZ2dlcmVkOicsIHtcclxuICAgICAgICAgIHVzZXI6IHVzZXIuZW1haWwsXHJcbiAgICAgICAgICBwcm92aWRlcjogYWNjb3VudD8ucHJvdmlkZXIsXHJcbiAgICAgICAgICBwcm9maWxlOiBwcm9maWxlPy5uYW1lXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmIChhY2NvdW50Py5wcm92aWRlciA9PT0gJ2dvb2dsZScpIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgR29vZ2xlIE9BdXRoIHNpZ24taW4gc3VjY2Vzc2Z1bCBmb3I6JywgdXNlci5lbWFpbCk7XHJcbiAgICAgICAgICAvLyBGb3Igbm93LCB3ZSdsbCBza2lwIGRhdGFiYXNlIG9wZXJhdGlvbnMgYW5kIGp1c3QgYWxsb3cgc2lnbi1pblxyXG4gICAgICAgICAgLy8gVE9ETzogUmUtZW5hYmxlIGRhdGFiYXNlIG9wZXJhdGlvbnMgb25jZSBjb25uZWN0aW9uIGlzIGZpeGVkXHJcbiAgICAgICAgICByZXR1cm4gdHJ1ZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGR1cmluZyBzaWduIGluOicsIGVycm9yKTtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgICBhc3luYyBqd3QoeyB0b2tlbiwgdXNlciwgdHJpZ2dlciwgc2Vzc2lvbiB9KSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SRIEpXVCBjYWxsYmFjayB0cmlnZ2VyZWQ6Jywge1xyXG4gICAgICAgIGhhc1VzZXI6ICEhdXNlcixcclxuICAgICAgICB0b2tlbkVtYWlsOiB0b2tlbi5lbWFpbCxcclxuICAgICAgICB1c2VyRW1haWw6IHVzZXI/LmVtYWlsLFxyXG4gICAgICAgIHRyaWdnZXJcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAodXNlcikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5GkIFNldHRpbmcgdXAgSldUIHRva2VuIGZvciBuZXcgdXNlcjonLCB1c2VyLmVtYWlsKTtcclxuICAgICAgICAvLyBGb3IgdGVzdGluZywgd2UnbGwgc2V0IGRlZmF1bHQgdmFsdWVzIHdpdGhvdXQgZGF0YWJhc2UgbG9va3VwXHJcbiAgICAgICAgdG9rZW4uaWQgPSB1c2VyLmlkIHx8IHVzZXIuZW1haWwgfHwgJ3RlbXAtaWQnOyAvLyBVc2UgZW1haWwgYXMgZmFsbGJhY2sgSURcclxuICAgICAgICB0b2tlbi5yb2xlID0gdW5kZWZpbmVkOyAvLyBDUklUSUNBTCBGSVg6IE5vIGRlZmF1bHQgcm9sZSAtIHVzZXIgbXVzdCBzZWxlY3QgZHVyaW5nIG9uYm9hcmRpbmdcclxuICAgICAgICB0b2tlbi5zdGF0dXMgPSAnQUNUSVZFJztcclxuICAgICAgICB0b2tlbi5sYW5ndWFnZSA9ICdhcic7XHJcbiAgICAgICAgdG9rZW4uZmlyc3ROYW1lID0gdXNlci5uYW1lPy5zcGxpdCgnICcpWzBdIHx8ICdVc2VyJztcclxuICAgICAgICB0b2tlbi5sYXN0TmFtZSA9IHVzZXIubmFtZT8uc3BsaXQoJyAnKS5zbGljZSgxKS5qb2luKCcgJykgfHwgJyc7XHJcbiAgICAgICAgdG9rZW4uYXZhdGFyID0gdXNlci5pbWFnZSB8fCBudWxsO1xyXG4gICAgICAgIHRva2VuLmhhc0NvbXBsZXRlZE9uYm9hcmRpbmcgPSBmYWxzZTsgLy8gQWx3YXlzIGZhbHNlIGZvciBuZXcgdXNlcnNcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBKV1QgdG9rZW4gY29uZmlndXJlZDonLCB7XHJcbiAgICAgICAgICBpZDogdG9rZW4uaWQsXHJcbiAgICAgICAgICByb2xlOiB0b2tlbi5yb2xlLFxyXG4gICAgICAgICAgaGFzQ29tcGxldGVkT25ib2FyZGluZzogdG9rZW4uaGFzQ29tcGxldGVkT25ib2FyZGluZ1xyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBIYW5kbGUgb25ib2FyZGluZyBjb21wbGV0aW9uIHVwZGF0ZXNcclxuICAgICAgaWYgKHRyaWdnZXIgPT09ICd1cGRhdGUnICYmIHNlc3Npb24pIHtcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBKV1QgdG9rZW4gdXBkYXRlIHRyaWdnZXJlZCB3aXRoIGRhdGE6Jywgc2Vzc2lvbik7XHJcblxyXG4gICAgICAgIC8vIFVwZGF0ZSByb2xlIGlmIHByb3ZpZGVkXHJcbiAgICAgICAgaWYgKHNlc3Npb24ucm9sZSkge1xyXG4gICAgICAgICAgdG9rZW4ucm9sZSA9IHNlc3Npb24ucm9sZTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgUm9sZSB1cGRhdGVkIGluIHRva2VuOicsIHNlc3Npb24ucm9sZSk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBVcGRhdGUgb25ib2FyZGluZyBjb21wbGV0aW9uIHN0YXR1cyBpZiBwcm92aWRlZFxyXG4gICAgICAgIGlmIChzZXNzaW9uLmhhc0NvbXBsZXRlZE9uYm9hcmRpbmcgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgdG9rZW4uaGFzQ29tcGxldGVkT25ib2FyZGluZyA9IHNlc3Npb24uaGFzQ29tcGxldGVkT25ib2FyZGluZztcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgT25ib2FyZGluZyBzdGF0dXMgdXBkYXRlZCBpbiB0b2tlbjonLCBzZXNzaW9uLmhhc0NvbXBsZXRlZE9uYm9hcmRpbmcpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIHRva2VuO1xyXG4gICAgfSxcclxuICAgIGFzeW5jIHNlc3Npb24oeyBzZXNzaW9uLCB0b2tlbiB9KSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OLIFNlc3Npb24gY2FsbGJhY2sgdHJpZ2dlcmVkOicsIHtcclxuICAgICAgICB0b2tlbkVtYWlsOiB0b2tlbi5lbWFpbCxcclxuICAgICAgICBzZXNzaW9uRW1haWw6IHNlc3Npb24udXNlcj8uZW1haWwsXHJcbiAgICAgICAgaGFzQ29tcGxldGVkT25ib2FyZGluZzogdG9rZW4uaGFzQ29tcGxldGVkT25ib2FyZGluZ1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGlmICh0b2tlbikge1xyXG4gICAgICAgIHNlc3Npb24udXNlci5pZCA9IHRva2VuLmlkIGFzIHN0cmluZztcclxuICAgICAgICBzZXNzaW9uLnVzZXIucm9sZSA9IHRva2VuLnJvbGUgYXMgc3RyaW5nO1xyXG4gICAgICAgIHNlc3Npb24udXNlci5zdGF0dXMgPSB0b2tlbi5zdGF0dXMgYXMgc3RyaW5nO1xyXG4gICAgICAgIHNlc3Npb24udXNlci5sYW5ndWFnZSA9IHRva2VuLmxhbmd1YWdlIGFzIHN0cmluZztcclxuICAgICAgICBzZXNzaW9uLnVzZXIuZmlyc3ROYW1lID0gdG9rZW4uZmlyc3ROYW1lIGFzIHN0cmluZztcclxuICAgICAgICBzZXNzaW9uLnVzZXIubGFzdE5hbWUgPSB0b2tlbi5sYXN0TmFtZSBhcyBzdHJpbmc7XHJcbiAgICAgICAgc2Vzc2lvbi51c2VyLmF2YXRhciA9IHRva2VuLmF2YXRhciBhcyBzdHJpbmc7XHJcbiAgICAgICAgc2Vzc2lvbi51c2VyLmhhc0NvbXBsZXRlZE9uYm9hcmRpbmcgPSB0b2tlbi5oYXNDb21wbGV0ZWRPbmJvYXJkaW5nIGFzIGJvb2xlYW47XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgU2Vzc2lvbiBjb25maWd1cmVkOicsIHtcclxuICAgICAgICAgIGlkOiBzZXNzaW9uLnVzZXIuaWQsXHJcbiAgICAgICAgICByb2xlOiBzZXNzaW9uLnVzZXIucm9sZSxcclxuICAgICAgICAgIGhhc0NvbXBsZXRlZE9uYm9hcmRpbmc6IHNlc3Npb24udXNlci5oYXNDb21wbGV0ZWRPbmJvYXJkaW5nXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIHNlc3Npb247XHJcbiAgICB9LFxyXG4gICAgYXN5bmMgcmVkaXJlY3QoeyB1cmwsIGJhc2VVcmwgfSkge1xyXG4gICAgICAvLyBIYW5kbGUgcm9sZS1iYXNlZCByZWRpcmVjdHMgYWZ0ZXIgc3VjY2Vzc2Z1bCBhdXRoZW50aWNhdGlvblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIE5leHRBdXRoIHJlZGlyZWN0IGNhbGxlZCB3aXRoOicsIHsgdXJsLCBiYXNlVXJsIH0pO1xyXG5cclxuICAgICAgICAvLyBDaGVjayBpZiB0aGlzIGlzIGEgcG9zdC1hdXRoZW50aWNhdGlvbiBjYWxsYmFja1xyXG4gICAgICAgIGlmICh1cmwuaW5jbHVkZXMoJy9hcGkvYXV0aC9jYWxsYmFjaycpIHx8IHVybCA9PT0gYmFzZVVybCB8fCB1cmwgPT09IGAke2Jhc2VVcmx9L2ApIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFBvc3QtYXV0aGVudGljYXRpb24gY2FsbGJhY2sgZGV0ZWN0ZWQnKTtcclxuXHJcbiAgICAgICAgICAvLyBDUklUSUNBTCBGSVg6IEFsd2F5cyByZWRpcmVjdCB0byBBSSBvbmJvYXJkaW5nIGZpcnN0IGZvciBwcm9wZXIgZmxvd1xyXG4gICAgICAgICAgLy8gVGhlIEFJIG9uYm9hcmRpbmcgcGFnZSB3aWxsIGhhbmRsZSB0aGUgbG9naWMgZm9yIGNvbXBsZXRlZCB1c2Vyc1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ/CfpJYgUmVkaXJlY3RpbmcgQUxMIGF1dGhlbnRpY2F0ZWQgdXNlcnMgdG8gQUkgb25ib2FyZGluZyBmb3IgcHJvcGVyIGZsb3cgaGFuZGxpbmcnKTtcclxuICAgICAgICAgIHJldHVybiBgJHtiYXNlVXJsfS9haS1vbmJvYXJkaW5nYDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEhhbmRsZSByZWxhdGl2ZSBVUkxzXHJcbiAgICAgICAgaWYgKHVybC5zdGFydHNXaXRoKCcvJykpIHtcclxuICAgICAgICAgIGNvbnN0IGZ1bGxVcmwgPSBgJHtiYXNlVXJsfSR7dXJsfWA7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBDb252ZXJ0aW5nIHJlbGF0aXZlIFVSTCB0byBhYnNvbHV0ZTonLCBmdWxsVXJsKTtcclxuICAgICAgICAgIHJldHVybiBmdWxsVXJsO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gSGFuZGxlIHNhbWUtb3JpZ2luIFVSTHNcclxuICAgICAgICBpZiAodXJsLnN0YXJ0c1dpdGgoYmFzZVVybCkpIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFNhbWUtb3JpZ2luIFVSTCByZWRpcmVjdDonLCB1cmwpO1xyXG4gICAgICAgICAgcmV0dXJuIHVybDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIERlZmF1bHQgZmFsbGJhY2sgLSByZWRpcmVjdCB0byBsYW5kaW5nIHBhZ2UgZm9yIHNhZmV0eVxyXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn4+gIEZhbGxiYWNrIHJlZGlyZWN0IHRvIGxhbmRpbmcgcGFnZScpO1xyXG4gICAgICAgIHJldHVybiBgJHtiYXNlVXJsfS8/YXV0aD1zdWNjZXNzYDtcclxuXHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIFJlZGlyZWN0IGVycm9yOicsIGVycm9yKTtcclxuICAgICAgICByZXR1cm4gYCR7YmFzZVVybH0vP2F1dGg9ZXJyb3JgO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSxcclxuICBzZXNzaW9uOiB7XHJcbiAgICBzdHJhdGVneTogJ2p3dCcsXHJcbiAgICBtYXhBZ2U6IDMwICogMjQgKiA2MCAqIDYwLCAvLyAzMCBkYXlzXHJcbiAgfSxcclxuICBqd3Q6IHtcclxuICAgIG1heEFnZTogMzAgKiAyNCAqIDYwICogNjAsIC8vIDMwIGRheXNcclxuICB9LFxyXG4gIGV2ZW50czoge1xyXG4gICAgYXN5bmMgc2lnbkluKHsgdXNlciwgYWNjb3VudCB9KSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKGDwn46JIFVzZXIgJHt1c2VyLmVtYWlsfSBzaWduZWQgaW4gd2l0aCAke2FjY291bnQ/LnByb3ZpZGVyfWApO1xyXG4gICAgICAvLyBUT0RPOiBSZS1lbmFibGUgZGF0YWJhc2Ugb3BlcmF0aW9ucyBvbmNlIGNvbm5lY3Rpb24gaXMgZml4ZWRcclxuICAgIH0sXHJcbiAgICBhc3luYyBzaWduT3V0KHsgdG9rZW4gfSkge1xyXG4gICAgICBjb25zb2xlLmxvZyhg8J+RiyBVc2VyICR7dG9rZW4/LmVtYWlsfSBzaWduZWQgb3V0YCk7XHJcbiAgICB9XHJcbiAgfSxcclxuICBkZWJ1ZzogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcsXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBOZXh0QXV0aChhdXRoT3B0aW9ucyk7XHJcbiJdLCJuYW1lcyI6WyJOZXh0QXV0aCIsIkdvb2dsZVByb3ZpZGVyIiwiYXV0aE9wdGlvbnMiLCJwcm92aWRlcnMiLCJjbGllbnRJZCIsInByb2Nlc3MiLCJlbnYiLCJHT09HTEVfQ0xJRU5UX0lEIiwiY2xpZW50U2VjcmV0IiwiR09PR0xFX0NMSUVOVF9TRUNSRVQiLCJhdXRob3JpemF0aW9uIiwicGFyYW1zIiwicHJvbXB0IiwiYWNjZXNzX3R5cGUiLCJyZXNwb25zZV90eXBlIiwicGFnZXMiLCJzaWduSW4iLCJlcnJvciIsImNhbGxiYWNrcyIsInVzZXIiLCJhY2NvdW50IiwicHJvZmlsZSIsImNvbnNvbGUiLCJsb2ciLCJlbWFpbCIsInByb3ZpZGVyIiwibmFtZSIsImp3dCIsInRva2VuIiwidHJpZ2dlciIsInNlc3Npb24iLCJoYXNVc2VyIiwidG9rZW5FbWFpbCIsInVzZXJFbWFpbCIsImlkIiwicm9sZSIsInVuZGVmaW5lZCIsInN0YXR1cyIsImxhbmd1YWdlIiwiZmlyc3ROYW1lIiwic3BsaXQiLCJsYXN0TmFtZSIsInNsaWNlIiwiam9pbiIsImF2YXRhciIsImltYWdlIiwiaGFzQ29tcGxldGVkT25ib2FyZGluZyIsInNlc3Npb25FbWFpbCIsInJlZGlyZWN0IiwidXJsIiwiYmFzZVVybCIsImluY2x1ZGVzIiwic3RhcnRzV2l0aCIsImZ1bGxVcmwiLCJzdHJhdGVneSIsIm1heEFnZSIsImV2ZW50cyIsInNpZ25PdXQiLCJkZWJ1ZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();